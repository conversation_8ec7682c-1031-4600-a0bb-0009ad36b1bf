2025-07-11 08:50:58 | DEBUG | scenario_agent:start_new_session:38 - [history][job_interview]:
2025-07-11 08:51:10 | DEBUG | scenario_agent:start_new_session:38 - [history][hotel_checkin]:
2025-07-11 08:51:27 | DEBUG | scenario_agent:start_new_session:38 - [history][job_interview]:AI: Can I confirm that you've applied for the Research and Development role we have available?
2025-07-11 09:08:43 | DEBUG | scenario_agent:start_new_session:38 - [history][job_interview]:AI: Can I confirm that you've applied for the Research and Development role we have available?
2025-07-11 09:08:44 | DEBUG | scenario_agent:start_new_session:38 - [history][hotel_checkin]:AI: Welcome to our hotel! Do you have a reservation with us?
2025-07-11 09:08:45 | DEBUG | scenario_agent:start_new_session:38 - [history][job_interview]:AI: Can I confirm that you've applied for the Research and Development role we have available?
2025-07-11 09:08:46 | DEBUG | scenario_agent:start_new_session:38 - [history][hotel_checkin]:AI: Welcome to our hotel! Do you have a reservation with us?
2025-07-11 09:08:47 | DEBUG | scenario_agent:start_new_session:38 - [history][job_interview]:AI: Can I confirm that you've applied for the Research and Development role we have available?
2025-07-11 09:08:48 | DEBUG | scenario_agent:start_new_session:38 - [history][hotel_checkin]:AI: Welcome to our hotel! Do you have a reservation with us?
2025-07-11 09:08:49 | DEBUG | scenario_agent:start_new_session:38 - [history][job_interview]:AI: Can I confirm that you've applied for the Research and Development role we have available?
2025-07-11 09:08:50 | DEBUG | scenario_agent:start_new_session:38 - [history][hotel_checkin]:AI: Welcome to our hotel! Do you have a reservation with us?
2025-07-11 09:08:51 | DEBUG | scenario_agent:start_new_session:38 - [history][job_interview]:AI: Can I confirm that you've applied for the Research and Development role we have available?
2025-07-11 09:08:52 | DEBUG | scenario_agent:start_new_session:38 - [history][hotel_checkin]:AI: Welcome to our hotel! Do you have a reservation with us?
2025-07-11 09:08:52 | DEBUG | scenario_agent:start_new_session:38 - [history][job_interview]:AI: Can I confirm that you've applied for the Research and Development role we have available?
2025-07-11 09:08:54 | DEBUG | scenario_agent:start_new_session:38 - [history][hotel_checkin]:AI: Welcome to our hotel! Do you have a reservation with us?
2025-07-11 09:09:50 | DEBUG | scenario_agent:start_new_session:38 - [history][job_interview]:AI: Can I confirm that you've applied for the Research and Development role we have available?
2025-07-11 09:09:51 | DEBUG | scenario_agent:start_new_session:38 - [history][hotel_checkin]:AI: Welcome to our hotel! Do you have a reservation with us?
2025-07-11 09:10:15 | DEBUG | scenario_agent:start_new_session:38 - [history][job_interview]:AI: Can I confirm that you've applied for the Research and Development role we have available?
2025-07-11 09:10:22 | DEBUG | scenario_agent:start_new_session:38 - [history][hotel_checkin]:AI: Welcome to our hotel! Do you have a reservation with us?
2025-07-11 09:10:27 | DEBUG | scenario_agent:start_new_session:38 - [history][job_interview]:AI: Can I confirm that you've applied for the Research and Development role we have available?
2025-07-11 09:10:28 | DEBUG | scenario_agent:start_new_session:38 - [history][hotel_checkin]:AI: Welcome to our hotel! Do you have a reservation with us?
2025-07-11 09:10:29 | DEBUG | scenario_agent:start_new_session:38 - [history][job_interview]:AI: Can I confirm that you've applied for the Research and Development role we have available?
2025-07-11 09:10:30 | DEBUG | scenario_agent:start_new_session:38 - [history][hotel_checkin]:AI: Welcome to our hotel! Do you have a reservation with us?
2025-07-11 09:10:31 | DEBUG | scenario_agent:start_new_session:38 - [history][job_interview]:AI: Can I confirm that you've applied for the Research and Development role we have available?
2025-07-11 09:10:33 | DEBUG | scenario_agent:start_new_session:38 - [history][hotel_checkin]:AI: Welcome to our hotel! Do you have a reservation with us?
2025-07-11 09:10:37 | DEBUG | scenario_agent:start_new_session:38 - [history][hotel_checkin]:AI: Welcome to our hotel! Do you have a reservation with us?
2025-07-11 09:17:18 | DEBUG | scenario_agent:start_new_session:38 - [history][job_interview]:AI: Can I confirm that you've applied for the Research and Development role we have available?
2025-07-11 09:17:19 | DEBUG | scenario_agent:start_new_session:38 - [history][hotel_checkin]:AI: Welcome to our hotel! Do you have a reservation with us?
2025-07-11 09:17:23 | DEBUG | scenario_agent:start_new_session:38 - [history][job_interview]:AI: Can I confirm that you've applied for the Research and Development role we have available?
2025-07-11 09:22:30 | DEBUG | scenario_agent:start_new_session:38 - [history][hotel_checkin]:AI: Welcome to our hotel! Do you have a reservation with us?
2025-07-11 09:23:27 | DEBUG | scenario_agent:start_new_session:38 - [history][hotel_checkin]:AI: Welcome to our hotel! Do you have a reservation with us?
2025-07-11 09:24:20 | DEBUG | scenario_agent:start_new_session:38 - [history][job_interview]:AI: Can I confirm that you've applied for the Research and Development role we have available?
2025-07-11 09:26:11 | DEBUG | agent_base:chat_with_history:107 - [ChatBot][conversation] Hi there! How can I help you with your English learning today? Would you like to practice English in a specific scenario like a technical interview, restaurant ordering, or hosting a meeting? Just let me know which one you'd like to try!
2025-07-11 09:26:11 | INFO | conversation_tab:handle_conversation:26 - [ChatBot]: Hi there! How can I help you with your English learning today? Would you like to practice English in a specific scenario like a technical interview, restaurant ordering, or hosting a meeting? Just let me know which one you'd like to try!
2025-07-11 09:29:23 | DEBUG | scenario_agent:start_new_session:38 - [history][job_interview]:
2025-07-11 09:29:24 | DEBUG | scenario_agent:start_new_session:38 - [history][hotel_checkin]:
2025-07-11 09:30:05 | DEBUG | scenario_agent:start_new_session:38 - [history][hotel_checkin]:AI: Hi there! How can I help you with your stay?
2025-07-11 09:30:05 | DEBUG | scenario_agent:start_new_session:38 - [history][job_interview]:AI: You are indeed the candidate we invited to interview for our R&D team, right?
2025-07-11 09:30:06 | DEBUG | scenario_agent:start_new_session:38 - [history][hotel_checkin]:AI: Hi there! How can I help you with your stay?
2025-07-11 09:30:07 | DEBUG | scenario_agent:start_new_session:38 - [history][job_interview]:AI: You are indeed the candidate we invited to interview for our R&D team, right?
2025-07-11 09:30:32 | DEBUG | scenario_agent:start_new_session:38 - [history][hotel_checkin]:AI: Hi there! How can I help you with your stay?
2025-07-11 09:30:34 | DEBUG | scenario_agent:start_new_session:38 - [history][job_interview]:AI: You are indeed the candidate we invited to interview for our R&D team, right?
2025-07-11 09:30:36 | DEBUG | scenario_agent:start_new_session:38 - [history][hotel_checkin]:AI: Hi there! How can I help you with your stay?
2025-07-11 09:30:37 | DEBUG | scenario_agent:start_new_session:38 - [history][job_interview]:AI: You are indeed the candidate we invited to interview for our R&D team, right?
2025-07-11 09:30:38 | DEBUG | scenario_agent:start_new_session:38 - [history][hotel_checkin]:AI: Hi there! How can I help you with your stay?
2025-07-11 09:30:38 | DEBUG | scenario_agent:start_new_session:38 - [history][job_interview]:AI: You are indeed the candidate we invited to interview for our R&D team, right?
2025-07-11 09:30:40 | DEBUG | scenario_agent:start_new_session:38 - [history][hotel_checkin]:AI: Hi there! How can I help you with your stay?
2025-07-11 09:30:41 | DEBUG | scenario_agent:start_new_session:38 - [history][job_interview]:AI: You are indeed the candidate we invited to interview for our R&D team, right?
2025-07-11 09:30:42 | DEBUG | scenario_agent:start_new_session:38 - [history][hotel_checkin]:AI: Hi there! How can I help you with your stay?
2025-07-11 09:46:47 | DEBUG | scenario_agent:start_new_session:38 - [history][job_interview]:AI: You are indeed the candidate we invited to interview for our R&D team, right?
2025-07-11 09:46:49 | DEBUG | scenario_agent:start_new_session:38 - [history][hotel_checkin]:AI: Hi there! How can I help you with your stay?
2025-07-11 09:46:50 | DEBUG | scenario_agent:start_new_session:38 - [history][job_interview]:AI: You are indeed the candidate we invited to interview for our R&D team, right?
2025-07-11 09:46:50 | DEBUG | scenario_agent:start_new_session:38 - [history][hotel_checkin]:AI: Hi there! How can I help you with your stay?
2025-07-11 09:46:51 | DEBUG | scenario_agent:start_new_session:38 - [history][job_interview]:AI: You are indeed the candidate we invited to interview for our R&D team, right?
2025-07-11 09:46:52 | DEBUG | scenario_agent:start_new_session:38 - [history][hotel_checkin]:AI: Hi there! How can I help you with your stay?
2025-07-11 09:46:55 | DEBUG | scenario_agent:start_new_session:38 - [history][job_interview]:AI: You are indeed the candidate we invited to interview for our R&D team, right?
2025-07-11 09:46:55 | DEBUG | scenario_agent:start_new_session:38 - [history][hotel_checkin]:AI: Hi there! How can I help you with your stay?
2025-07-11 09:46:56 | DEBUG | scenario_agent:start_new_session:38 - [history][job_interview]:AI: You are indeed the candidate we invited to interview for our R&D team, right?
2025-07-11 09:59:35 | DEBUG | agent_base:chat_with_history:107 - [ChatBot][conversation] Great! Let’s start by practicing ordering food in a restaurant. Imagine you are at a restaurant and ready to order. How would you start? Let's begin! 

Waiter: Good evening! Welcome to our restaurant. Are you ready to order?
2025-07-11 09:59:35 | INFO | conversation_tab:handle_conversation:26 - [ChatBot]: Great! Let’s start by practicing ordering food in a restaurant. Imagine you are at a restaurant and ready to order. How would you start? Let's begin! 

Waiter: Good evening! Welcome to our restaurant. Are you ready to order?
2025-07-11 10:00:56 | DEBUG | vocab_agent:restart_session:39 - [history][vocab_study]:
2025-07-11 10:01:00 | DEBUG | agent_base:chat_with_history:107 - [ChatBot][vocab_study] **RandyXu**: "Welcome! I'm **RandyXu**, your Language Mentor. Today, you will learn **5 new words** as below."

1: Persuade
- **说服，劝说**, Verb
- 第三人称单数 persuades；现在分词 persuading；过去式 persuaded；过去分词 persuaded
- **To cause someone to do something through reasoning, argument, or entreaty.**
- **例句**: "He tried to persuade his boss to give him a promotion."

2: Consequence
- **后果，结果**, Noun
- **A result or effect of an action or condition.**
- **例句**: "Not studying for the exam will have serious consequences."

3: Enhance
- **增强，提高**, Verb
- 第三人称单数 enhances；现在分词 enhancing；过去式 enhanced；过去分词 enhanced
- **To intensify, increase, or improve the quality, value, or extent of something.**
- **例句**: "Adding a new coat of paint will enhance the appearance of the room."

4: Enrich
- **丰富，使富裕**, Verb
- 第三人称单数 enriches；现在分词 enriching；过去式 enriched；过去分词 enriched
- **To improve the quality or value of something by adding desirable qualities.**
- **例句**: "Reading books can enrich your knowledge and vocabulary."

5: Diverse
- **多样的，不同的**, Adjective
- **Showing a great deal of variety; very different.**
- **例句**: "The city is known for its diverse culture and traditions."

以上就是我们今天要掌握的单词，现在让我们开始通过对话来熟练使用吧！
2025-07-11 10:01:13 | DEBUG | vocab_agent:restart_session:39 - [history][vocab_study]:
2025-07-11 10:01:17 | DEBUG | agent_base:chat_with_history:107 - [ChatBot][vocab_study] - **RandyXu**: "Welcome! I'm **RandyXu**, your Language Mentor. Today, you will learn **5 new words** as below."

1: Diverse
- **多样的，不同的**, Adjective
- **Showing a great deal of variety; very different.**
- **例句**: "Our company values a diverse workforce to bring different perspectives to the table."

2: Enhance
- **增强，增进**, Verb
- 第三人称单数 enhances；现在分词 enhancing；过去式 enhanced；过去分词 enhanced
- **To intensify, increase, or further improve the quality, value, or extent of something.**
- **例句**: "Exercise can enhance your overall well-being."

3: Potential
- **潜力，潜在的**, Noun/Adjective
- **Having or showing the capacity to develop into something in the future.**
- **例句**: "The new project has great potential for success."

4: Generate
- **产生，生成**, Verb
- 第三人称单数 generates；现在分词 generating；过去式 generated；过去分词 generated
- **To produce or create something.**
- **例句**: "Renewable energy sources can generate electricity without harming the environment."

5: Abundant
- **丰富的，大量的**, Adjective
- **Existing or available in large quantities; plentiful.**
- **例句**: "The tropical region is abundant in exotic fruits."

以上就是我们今天要掌握的单词，现在让我们开始通过对话来熟练使用吧！
2025-07-11 10:01:21 | DEBUG | vocab_agent:restart_session:39 - [history][vocab_study]:
2025-07-11 10:01:24 | DEBUG | agent_base:chat_with_history:107 - [ChatBot][vocab_study] **RandyXu**: "Welcome! I'm **RandyXu**, your Language Mentor. Today, you will learn **5 new words** as below."

1: Diverse
- **多种多样的，不同的**, Adjective
- **Showing a great deal of variety; very different.**
- **例句**: "The team is diverse, with members from different backgrounds."

2: Implement
- **实施，执行**, Verb
- 第三人称单数 implements；现在分词 implementing；过去式 implemented；过去分词 implemented
- **To put a decision, plan, agreement, etc. into effect.**
- **例句**: "The new policy will be implemented next month."

3: Enhance
- **增强，提高**, Verb
- 第三人称单数 enhances；现在分词 enhancing；过去式 enhanced；过去分词 enhanced
- **To increase or further improve the quality, value, or extent of.**
- **例句**: "Regular exercise can enhance your overall well-being."

4: Resilient
- **有弹性的，有韧性的**, Adjective
- **Able to withstand or recover quickly from difficult conditions.**
- **例句**: "She proved to be resilient in the face of adversity."

5: Collaborate
- **合作，协作**, Verb
- 第三人称单数 collaborates；现在分词 collaborating；过去式 collaborated；过去分词 collaborated
- **To work jointly with others to create or achieve something.**
- **例句**: "The two companies decided to collaborate on a new project."

以上就是我们今天要掌握的单词，现在让我们开始通过对话来熟练使用吧！
  
**场景说明**: We are discussing a group project for school where we need to implement different ideas from diverse team members.

**RandyXu**: "How can we enhance our project's outcome by being a diverse group?"

**提示例句**: "I believe having a diverse team can bring in unique perspectives and enhance our creativity."
2025-07-11 10:11:49 | DEBUG | scenario_agent:start_new_session:38 - [history][job_interview]:AI: You are indeed the candidate we invited to interview for our R&D team, right?
2025-07-14 09:58:37 | INFO | main_html:check_dependencies:67 - 依赖项检查通过
2025-07-14 09:58:37 | INFO | main_html:check_environment:82 - 已加载环境配置文件: C:\Users\<USER>\Desktop\Test\LanguageMentor\LanguageMentor\.env
2025-07-14 09:58:37 | INFO | main_html:check_environment:110 - 环境检查通过
2025-07-14 09:58:37 | INFO | main_html:start_api_server:24 - 正在启动 LanguageMentor API 服务器...
2025-07-14 09:58:37 | INFO | main_html:start_web_server:43 - 正在启动 Web 服务器，端口: 8000
2025-07-14 09:58:37 | INFO | main_html:start_web_server:44 - Web 界面地址: http://localhost:8000
2025-07-14 09:58:41 | INFO | main_html:open_browser:56 - 已在浏览器中打开 LanguageMentor
2025-07-14 09:58:52 | ERROR | app:conversation_chat:170 - 自由对话失败: Error code: 403 - {'error': {'message': '主账户可用余额不足，本次请求需要可用余额大于 0.02 元才能完成，您的可用余额为 -0.0077626 元(请求预冻结 0.02464125 元)，请充值后再使用', 'type': 'error'}}
2025-07-14 09:59:17 | ERROR | app:conversation_chat:170 - 自由对话失败: Error code: 403 - {'error': {'message': '主账户可用余额不足，本次请求需要可用余额大于 0.02 元才能完成，您的可用余额为 -0.0077626 元(请求预冻结 0.02464125 元)，请充值后再使用', 'type': 'error'}}
2025-07-14 10:09:17 | INFO | main_html:check_dependencies:69 - 依赖项检查通过
2025-07-14 10:09:17 | INFO | main_html:check_environment:84 - 已加载环境配置文件: C:\Users\<USER>\Desktop\Test\LanguageMentor\LanguageMentor\.env
2025-07-14 10:09:17 | INFO | main_html:check_environment:112 - 环境检查通过
2025-07-14 10:09:17 | INFO | main_html:start_api_server:25 - 正在启动 LanguageMentor API 服务器...
2025-07-14 10:09:17 | INFO | main_html:start_web_server:45 - 正在启动 Web 服务器，端口: 8000
2025-07-14 10:09:17 | INFO | main_html:start_web_server:46 - Web 界面地址: http://localhost:8000
2025-07-14 10:09:21 | INFO | main_html:open_browser:58 - 已在浏览器中打开 LanguageMentor
2025-07-14 10:09:41 | ERROR | app:conversation_chat:170 - 自由对话失败: Error code: 403 - {'error': {'message': '主账户可用余额不足，本次请求需要可用余额大于 0.02 元才能完成，您的可用余额为 -0.0077626 元(请求预冻结 0.02464125 元)，请充值后再使用', 'type': 'error'}}
2025-07-14 10:09:52 | ERROR | app:vocab_chat:255 - 词汇学习对话失败: Error code: 403 - {'error': {'message': '主账户可用余额不足，本次请求需要可用余额大于 0.03 元才能完成，您的可用余额为 -0.00834385 元(请求预冻结 0.0252225 元)，请充值后再使用', 'type': 'error'}}
2025-07-14 10:20:33 | INFO | main_html:check_dependencies:69 - 依赖项检查通过
2025-07-14 10:20:33 | INFO | main_html:check_environment:84 - 已加载环境配置文件: C:\Users\<USER>\Desktop\Test\LanguageMentor\LanguageMentor\.env
2025-07-14 10:20:33 | INFO | main_html:check_environment:112 - 环境检查通过
2025-07-14 10:20:33 | INFO | main_html:start_api_server:25 - 正在启动 LanguageMentor API 服务器...
2025-07-14 10:20:33 | INFO | main_html:start_web_server:45 - 正在启动 Web 服务器，端口: 8000
2025-07-14 10:20:33 | INFO | main_html:start_web_server:46 - Web 界面地址: http://localhost:8000
2025-07-14 10:20:37 | INFO | main_html:open_browser:58 - 已在浏览器中打开 LanguageMentor
2025-07-14 10:20:46 | ERROR | app:conversation_chat:170 - 自由对话失败: Error code: 403 - {'error': {'message': '主账户可用余额不足，本次请求需要可用余额大于 0.02 元才能完成，您的可用余额为 -0.0077626 元(请求预冻结 0.02464125 元)，请充值后再使用', 'type': 'error'}}
2025-07-14 10:23:08 | INFO | main_html:main:166 - 收到中断信号，正在关闭服务...
2025-07-14 10:23:18 | INFO | main_html:check_dependencies:69 - 依赖项检查通过
2025-07-14 10:23:18 | INFO | main_html:check_environment:84 - 已加载环境配置文件: C:\Users\<USER>\Desktop\Test\LanguageMentor\LanguageMentor\.env
2025-07-14 10:23:18 | INFO | main_html:check_environment:112 - 环境检查通过
2025-07-14 10:23:18 | INFO | main_html:start_api_server:25 - 正在启动 LanguageMentor API 服务器...
2025-07-14 10:23:18 | INFO | main_html:start_web_server:45 - 正在启动 Web 服务器，端口: 8000
2025-07-14 10:23:18 | INFO | main_html:start_web_server:46 - Web 界面地址: http://localhost:8000
2025-07-14 10:23:22 | INFO | main_html:open_browser:58 - 已在浏览器中打开 LanguageMentor
2025-07-14 10:23:28 | ERROR | app:conversation_chat:170 - 自由对话失败: Error code: 403 - {'error': {'message': '主账户可用余额不足，本次请求需要可用余额大于 0.02 元才能完成，您的可用余额为 -0.0077626 元(请求预冻结 0.02464125 元)，请充值后再使用', 'type': 'error'}}
2025-07-14 10:25:16 | INFO | main_html:check_dependencies:69 - 依赖项检查通过
2025-07-14 10:25:16 | INFO | main_html:check_environment:84 - 已加载环境配置文件: C:\Users\<USER>\Desktop\Test\LanguageMentor\LanguageMentor\.env
2025-07-14 10:25:16 | INFO | main_html:check_environment:112 - 环境检查通过
2025-07-14 10:25:16 | INFO | main_html:start_api_server:25 - 正在启动 LanguageMentor API 服务器...
2025-07-14 10:25:16 | ERROR | main_html:start_web_server:50 - Web服务器启动失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-07-14 10:27:58 | INFO | main_html:check_dependencies:69 - 依赖项检查通过
2025-07-14 10:27:58 | INFO | main_html:check_environment:84 - 已加载环境配置文件: C:\Users\<USER>\Desktop\Test\LanguageMentor\LanguageMentor\.env
2025-07-14 10:27:58 | INFO | main_html:check_environment:112 - 环境检查通过
2025-07-14 10:27:58 | INFO | main_html:start_api_server:25 - 正在启动 LanguageMentor API 服务器...
2025-07-14 10:27:58 | ERROR | main_html:start_web_server:50 - Web服务器启动失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-07-14 10:29:18 | INFO | main_html:check_dependencies:69 - 依赖项检查通过
2025-07-14 10:29:18 | INFO | main_html:check_environment:84 - 已加载环境配置文件: C:\Users\<USER>\Desktop\Test\LanguageMentor\LanguageMentor\.env
2025-07-14 10:29:18 | INFO | main_html:check_environment:112 - 环境检查通过
2025-07-14 10:29:18 | INFO | main_html:start_api_server:25 - 正在启动 LanguageMentor API 服务器...
2025-07-14 10:29:18 | INFO | main_html:start_web_server:45 - 正在启动 Web 服务器，端口: 8001
2025-07-14 10:29:18 | INFO | main_html:start_web_server:46 - Web 界面地址: http://localhost:8001
2025-07-14 10:29:22 | INFO | main_html:open_browser:58 - 已在浏览器中打开 LanguageMentor
2025-07-14 10:29:29 | ERROR | app:conversation_chat:170 - 自由对话失败: Error code: 403 - {'error': {'message': '主账户可用余额不足，本次请求需要可用余额大于 0.02 元才能完成，您的可用余额为 -0.0077626 元(请求预冻结 0.02464125 元)，请充值后再使用', 'type': 'error'}}
2025-07-14 10:29:44 | ERROR | app:conversation_chat:170 - 自由对话失败: Error code: 403 - {'error': {'message': '主账户可用余额不足，本次请求需要可用余额大于 0.02 元才能完成，您的可用余额为 -0.0077626 元(请求预冻结 0.02464125 元)，请充值后再使用', 'type': 'error'}}
2025-07-14 10:48:02 | INFO | main_html:check_dependencies:69 - 依赖项检查通过
2025-07-14 10:48:02 | INFO | main_html:check_environment:84 - 已加载环境配置文件: C:\Users\<USER>\Desktop\Test\LanguageMentor\LanguageMentor\.env
2025-07-14 10:48:02 | INFO | main_html:check_environment:112 - 环境检查通过
2025-07-14 10:48:02 | INFO | main_html:start_api_server:25 - 正在启动 LanguageMentor API 服务器...
2025-07-14 10:48:02 | INFO | main_html:start_web_server:45 - 正在启动 Web 服务器，端口: 8001
2025-07-14 10:48:02 | INFO | main_html:start_web_server:46 - Web 界面地址: http://localhost:8001
2025-07-14 10:48:06 | INFO | main_html:open_browser:58 - 已在浏览器中打开 LanguageMentor
2025-07-14 10:48:17 | DEBUG | agent_base:chat_with_history:107 - [ChatBot][conversation] 你好！你想要练习哪方面的英语对话？比如技术面试、餐厅点餐还是主持会议呢？
2025-07-14 10:48:22 | ERROR | app:start_scenario:107 - 开始场景对话失败: 找不到提示文件 prompts/job_interview_prompt.txt!
2025-07-14 10:48:26 | ERROR | app:start_scenario:107 - 开始场景对话失败: 找不到提示文件 prompts/hotel_checkin_prompt.txt!
2025-07-14 10:48:32 | DEBUG | vocab_agent:restart_session:39 - [history][vocab_1752461312311]:
2025-07-14 10:48:43 | DEBUG | agent_base:chat_with_history:107 - [ChatBot][vocab_study] - **RandyXu**: "Welcome! I'm **RandyXu**, your Language Mentor. Today, you will learn **5 new words** as below."

1: Resilient
- **有弹性的，能恢复的**, Adjective
- **Able to withstand or recover quickly from difficult conditions.**
- **例句**: "She showed how resilient she was by bouncing back from failure."

2: Diverse
- **多样的，不同的**, Adjective
- **Showing a great deal of variety; very different.**
- **例句**: "The company has a diverse workforce with employees from various cultural backgrounds."

3: Enhance
- **提升，增强**, Verb
- Third person singular enhances; Present participle enhancing; Past simple enhanced; Past participle enhanced
- **To intensify, increase, or improve the quality, value, or attractiveness of something.**
- **例句**: "Regular exercise can enhance your overall health."

4: Comprehend
- **理解，领会**, Verb
- Third person singular comprehends; Present participle comprehending; Past simple comprehended; Past participle comprehended
- **To grasp mentally; understand.**
- **例句**: "It is important to comprehend the instructions before starting the task."

5: Contribute
- **贡献，捐助**, Verb
- Third person singular contributes; Present participle contributing; Past simple contributed; Past participle contributed
- **To give (something, especially money or goods) in order to help achieve or provide something.**
- **例句**: "Everyone should contribute to protecting the environment."

以上就是我们今天要掌握的单词，现在让我们开始通过对话来熟练使用吧！
2025-07-14 10:49:02 | ERROR | app:start_scenario:107 - 开始场景对话失败: 找不到提示文件 prompts/job_interview_prompt.txt!
2025-07-14 10:49:05 | ERROR | app:start_scenario:107 - 开始场景对话失败: 找不到提示文件 prompts/hotel_checkin_prompt.txt!
2025-07-14 10:50:50 | DEBUG | agent_base:chat_with_history:107 - [ChatBot][conversation] 对不起，这是一个英语对话练习环节。我们可以开始模拟一个技术面试的对话。你准备好了吗？让我们开始吧！请你自我介绍一下。 

1. RandyXu: Hello, please introduce yourself.
2. Student: Hello, my name is [Student's Name].
3. RandyXu: Nice to meet you, [Student's Name]. Can you tell me about your technical background and experience?
4. Student: I have a degree in computer science and two years of experience as a software developer.
5. RandyXu: Great! Could you share a challenging project you've worked on and how you overcame obstacles during the development process?
6. Student: I worked on a mobile app where I had to optimize the code for better performance. I faced issues with memory management but solved them by conducting code reviews with senior developers.
7. RandyXu: That's impressive. Now, let's move on to a technical question. Can you explain the difference between Java and JavaScript?
8. Student: Java is a statically typed language used for backend development, while JavaScript is a dynamically typed language primarily used for frontend web development.
9. RandyXu: Good job! Now, let's shift to a behavioral question. How do you handle working under tight deadlines and pressure in a team setting?
10. Student: I prioritize tasks, communicate effectively with team members, and remain calm to ensure we meet our deadlines.

结束后，我们会讨论并进行反馈。让我们继续练习！
2025-07-14 10:51:53 | ERROR | app:start_scenario:107 - 开始场景对话失败: 找不到提示文件 prompts/job_interview_prompt.txt!
2025-07-14 10:51:54 | ERROR | app:start_scenario:107 - 开始场景对话失败: 找不到提示文件 prompts/hotel_checkin_prompt.txt!
2025-07-14 10:52:02 | ERROR | app:start_scenario:107 - 开始场景对话失败: 找不到提示文件 prompts/job_interview_prompt.txt!
2025-07-14 10:52:16 | ERROR | app:start_scenario:107 - 开始场景对话失败: 找不到提示文件 prompts/hotel_checkin_prompt.txt!
2025-07-14 10:53:00 | INFO | main_html:check_dependencies:69 - 依赖项检查通过
2025-07-14 10:53:00 | INFO | main_html:check_environment:84 - 已加载环境配置文件: C:\Users\<USER>\Desktop\Test\LanguageMentor\LanguageMentor\.env
2025-07-14 10:53:01 | INFO | main_html:check_environment:112 - 环境检查通过
2025-07-14 10:53:01 | INFO | main_html:start_api_server:25 - 正在启动 LanguageMentor API 服务器...
2025-07-14 10:53:01 | INFO | main_html:start_web_server:45 - 正在启动 Web 服务器，端口: 8001
2025-07-14 10:53:01 | INFO | main_html:start_web_server:46 - Web 界面地址: http://localhost:8001
2025-07-14 10:53:07 | INFO | main_html:open_browser:58 - 已在浏览器中打开 LanguageMentor
2025-07-14 10:53:11 | ERROR | app:start_scenario:107 - 开始场景对话失败: 找不到提示文件 prompts/job_interview_prompt.txt!
2025-07-14 10:53:19 | ERROR | app:start_scenario:107 - 开始场景对话失败: 找不到提示文件 prompts/hotel_checkin_prompt.txt!
2025-07-14 10:55:26 | INFO | main_html:check_dependencies:69 - 依赖项检查通过
2025-07-14 10:55:26 | INFO | main_html:check_environment:84 - 已加载环境配置文件: C:\Users\<USER>\Desktop\Test\LanguageMentor\LanguageMentor\.env
2025-07-14 10:55:26 | INFO | main_html:check_environment:112 - 环境检查通过
2025-07-14 10:55:26 | INFO | main_html:start_api_server:25 - 正在启动 LanguageMentor API 服务器...
2025-07-14 10:55:26 | ERROR | main_html:start_web_server:50 - Web服务器启动失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
