#!/usr/bin/env python3
"""
LanguageMentor HTML版本安装脚本
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """运行命令并处理错误"""
    print(f"📦 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} 完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败:")
        print(f"   错误: {e.stderr}")
        return False

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True

def install_dependencies():
    """安装依赖项"""
    dependencies = [
        "flask>=2.3.0",
        "flask-cors>=4.0.0",
        "langchain>=0.1.0",
        "langchain-openai>=0.0.5",
        "langchain-core>=0.1.0",
        "openai>=1.0.0",
        "python-dotenv>=1.0.0",
        "loguru>=0.7.0",
        "pydantic>=2.0.0",
        "requests>=2.31.0"
    ]
    
    print("📦 安装HTML版本依赖项...")
    for dep in dependencies:
        if not run_command(f"pip install {dep}", f"安装 {dep.split('>=')[0]}"):
            return False
    
    return True

def create_env_template():
    """创建环境变量模板文件"""
    env_template = """# LanguageMentor 环境配置
# 请填入你的OpenAI API配置

# OpenAI API Key (必需)
OPENAI_API_KEY=your_api_key_here

# OpenAI API Base URL (可选，默认为官方API)
OPENAI_API_BASE=https://api.openai.com/v1

# 其他可选配置
# OPENAI_MODEL=gpt-3.5-turbo
# MAX_TOKENS=4096
# TEMPERATURE=0.8
"""
    
    env_file = Path(".env.template")
    if not env_file.exists():
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(env_template)
        print("✅ 已创建环境配置模板文件: .env.template")
        print("   请复制为 .env 文件并填入你的API配置")
    
    return True

def check_project_structure():
    """检查项目结构"""
    required_dirs = ['agents', 'prompts', 'content', 'web', 'api', 'utils']
    required_files = [
        'web/index.html',
        'web/js/main.js',
        'web/js/api.js',
        'web/js/components.js',
        'web/css/main.css',
        'web/css/components.css',
        'api/app.py',
        'main_html.py'
    ]
    
    print("🔍 检查项目结构...")
    
    # 检查目录
    for dir_name in required_dirs:
        if not Path(dir_name).exists():
            print(f"❌ 缺少目录: {dir_name}")
            return False
    
    # 检查文件
    for file_path in required_files:
        if not Path(file_path).exists():
            print(f"❌ 缺少文件: {file_path}")
            return False
    
    print("✅ 项目结构检查通过")
    return True

def print_success_message():
    """打印成功信息"""
    print("\n" + "="*60)
    print("🎉 LanguageMentor HTML版本安装完成！")
    print("="*60)
    print("\n📋 下一步操作:")
    print("1. 配置API Key:")
    print("   • 复制 .env.template 为 .env")
    print("   • 在 .env 文件中填入你的 OpenAI API Key")
    print("   • 或在Web界面的设置中配置")
    print("\n2. 启动应用:")
    print("   python main_html.py")
    print("\n3. 访问界面:")
    print("   🌐 Web界面: http://localhost:8000")
    print("   🔧 API服务: http://localhost:5000")
    print("\n💡 提示:")
    print("   • 首次使用请在设置中配置API Key")
    print("   • 支持响应式设计，可在手机上使用")
    print("   • 保留了原有的所有智能体功能")
    print("="*60)

def main():
    """主函数"""
    print("🚀 开始安装 LanguageMentor HTML版本")
    print("="*50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 检查项目结构
    if not check_project_structure():
        print("\n❌ 项目结构不完整，请确保所有文件都已创建")
        sys.exit(1)
    
    # 安装依赖项
    if not install_dependencies():
        print("\n❌ 依赖项安装失败")
        sys.exit(1)
    
    # 创建环境配置模板
    create_env_template()
    
    # 打印成功信息
    print_success_message()

if __name__ == "__main__":
    main()
