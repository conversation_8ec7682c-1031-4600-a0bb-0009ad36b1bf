<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="32">
            <item index="0" class="java.lang.String" itemvalue="sse_starlette" />
            <item index="1" class="java.lang.String" itemvalue="sentence_transformers" />
            <item index="2" class="java.lang.String" itemvalue="rapidocr_onnxruntime" />
            <item index="3" class="java.lang.String" itemvalue="transformers_stream_generator" />
            <item index="4" class="java.lang.String" itemvalue="httpx_sse" />
            <item index="5" class="java.lang.String" itemvalue="python_dotenv" />
            <item index="6" class="java.lang.String" itemvalue="torch" />
            <item index="7" class="java.lang.String" itemvalue="torchvision" />
            <item index="8" class="java.lang.String" itemvalue="torchaudio" />
            <item index="9" class="java.lang.String" itemvalue="cpm_kernels" />
            <item index="10" class="java.lang.String" itemvalue="duckduckgo_search" />
            <item index="11" class="java.lang.String" itemvalue="rouge_chinese" />
            <item index="12" class="java.lang.String" itemvalue="ruamel_yaml" />
            <item index="13" class="java.lang.String" itemvalue="wandb" />
            <item index="14" class="java.lang.String" itemvalue="beautifulsoup4" />
            <item index="15" class="java.lang.String" itemvalue="tiktoken" />
            <item index="16" class="java.lang.String" itemvalue="pydantic" />
            <item index="17" class="java.lang.String" itemvalue="scikit_learn" />
            <item index="18" class="java.lang.String" itemvalue="ipykernel" />
            <item index="19" class="java.lang.String" itemvalue="imap_tools" />
            <item index="20" class="java.lang.String" itemvalue="pandas" />
            <item index="21" class="java.lang.String" itemvalue="python_docx" />
            <item index="22" class="java.lang.String" itemvalue="tqdm" />
            <item index="23" class="java.lang.String" itemvalue="openai" />
            <item index="24" class="java.lang.String" itemvalue="google-generativeai" />
            <item index="25" class="java.lang.String" itemvalue="faiss_cpu" />
            <item index="26" class="java.lang.String" itemvalue="langchain" />
            <item index="27" class="java.lang.String" itemvalue="tenacity" />
            <item index="28" class="java.lang.String" itemvalue="anthropic" />
            <item index="29" class="java.lang.String" itemvalue="aiohttp" />
            <item index="30" class="java.lang.String" itemvalue="websockets" />
            <item index="31" class="java.lang.String" itemvalue="rotary_embedding_torch" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>