Requirement already satisfied: langchain-openai in c:\users\<USER>\anaconda3\lib\site-packages (0.1.25)
Requirement already satisfied: openai<2.0.0,>=1.40.0 in c:\users\<USER>\anaconda3\lib\site-packages (from langchain-openai) (1.93.0)
Requirement already satisfied: langchain-core<0.3.0,>=0.2.40 in c:\users\<USER>\anaconda3\lib\site-packages (from langchain-openai) (0.2.43)
Requirement already satisfied: tiktoken<1,>=0.7 in c:\users\<USER>\anaconda3\lib\site-packages (from langchain-openai) (0.9.0)
Requirement already satisfied: typing-extensions>=4.7 in c:\users\<USER>\anaconda3\lib\site-packages (from langchain-core<0.3.0,>=0.2.40->langchain-openai) (4.14.1)
Requirement already satisfied: pydantic<3,>=1 in c:\users\<USER>\anaconda3\lib\site-packages (from langchain-core<0.3.0,>=0.2.40->langchain-openai) (2.11.7)
Requirement already satisfied: jsonpatch<2.0,>=1.33 in c:\users\<USER>\anaconda3\lib\site-packages (from langchain-core<0.3.0,>=0.2.40->langchain-openai) (1.33)
Requirement already satisfied: langsmith<0.2.0,>=0.1.112 in c:\users\<USER>\anaconda3\lib\site-packages (from langchain-core<0.3.0,>=0.2.40->langchain-openai) (0.1.147)
Requirement already satisfied: PyYAML>=5.3 in c:\users\<USER>\anaconda3\lib\site-packages (from langchain-core<0.3.0,>=0.2.40->langchain-openai) (6.0)
Requirement already satisfied: tenacity!=8.4.0,<9.0.0,>=8.1.0 in c:\users\<USER>\anaconda3\lib\site-packages (from langchain-core<0.3.0,>=0.2.40->langchain-openai) (8.5.0)
Requirement already satisfied: packaging<25,>=23.2 in c:\users\<USER>\anaconda3\lib\site-packages (from langchain-core<0.3.0,>=0.2.40->langchain-openai) (24.2)
Requirement already satisfied: anyio<5,>=3.5.0 in c:\users\<USER>\anaconda3\lib\site-packages (from openai<2.0.0,>=1.40.0->langchain-openai) (4.9.0)
Requirement already satisfied: sniffio in c:\users\<USER>\anaconda3\lib\site-packages (from openai<2.0.0,>=1.40.0->langchain-openai) (1.2.0)
Requirement already satisfied: tqdm>4 in c:\users\<USER>\anaconda3\lib\site-packages (from openai<2.0.0,>=1.40.0->langchain-openai) (4.64.1)
Requirement already satisfied: httpx<1,>=0.23.0 in c:\users\<USER>\anaconda3\lib\site-packages (from openai<2.0.0,>=1.40.0->langchain-openai) (0.28.1)
Requirement already satisfied: jiter<1,>=0.4.0 in c:\users\<USER>\anaconda3\lib\site-packages (from openai<2.0.0,>=1.40.0->langchain-openai) (0.10.0)
Requirement already satisfied: distro<2,>=1.7.0 in c:\users\<USER>\anaconda3\lib\site-packages (from openai<2.0.0,>=1.40.0->langchain-openai) (1.9.0)
Requirement already satisfied: requests>=2.26.0 in c:\users\<USER>\anaconda3\lib\site-packages (from tiktoken<1,>=0.7->langchain-openai) (2.32.4)
Requirement already satisfied: regex>=2022.1.18 in c:\users\<USER>\anaconda3\lib\site-packages (from tiktoken<1,>=0.7->langchain-openai) (2024.11.6)
Requirement already satisfied: exceptiongroup>=1.0.2 in c:\users\<USER>\anaconda3\lib\site-packages (from anyio<5,>=3.5.0->openai<2.0.0,>=1.40.0->langchain-openai) (1.1.1)
Requirement already satisfied: idna>=2.8 in c:\users\<USER>\anaconda3\lib\site-packages (from anyio<5,>=3.5.0->openai<2.0.0,>=1.40.0->langchain-openai) (3.4)
Requirement already satisfied: httpcore==1.* in c:\users\<USER>\anaconda3\lib\site-packages (from httpx<1,>=0.23.0->openai<2.0.0,>=1.40.0->langchain-openai) (1.0.9)
Requirement already satisfied: certifi in c:\users\<USER>\anaconda3\lib\site-packages (from httpx<1,>=0.23.0->openai<2.0.0,>=1.40.0->langchain-openai) (2025.1.31)
Requirement already satisfied: h11>=0.16 in c:\users\<USER>\anaconda3\lib\site-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai<2.0.0,>=1.40.0->langchain-openai) (0.16.0)
Requirement already satisfied: jsonpointer>=1.9 in c:\users\<USER>\anaconda3\lib\site-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.3.0,>=0.2.40->langchain-openai) (2.1)
Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in c:\users\<USER>\anaconda3\lib\site-packages (from langsmith<0.2.0,>=0.1.112->langchain-core<0.3.0,>=0.2.40->langchain-openai) (1.0.0)
Requirement already satisfied: orjson<4.0.0,>=3.9.14 in c:\users\<USER>\anaconda3\lib\site-packages (from langsmith<0.2.0,>=0.1.112->langchain-core<0.3.0,>=0.2.40->langchain-openai) (3.10.18)
Requirement already satisfied: typing-inspection>=0.4.0 in c:\users\<USER>\anaconda3\lib\site-packages (from pydantic<3,>=1->langchain-core<0.3.0,>=0.2.40->langchain-openai) (0.4.1)
Requirement already satisfied: annotated-types>=0.6.0 in c:\users\<USER>\anaconda3\lib\site-packages (from pydantic<3,>=1->langchain-core<0.3.0,>=0.2.40->langchain-openai) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in c:\users\<USER>\anaconda3\lib\site-packages (from pydantic<3,>=1->langchain-core<0.3.0,>=0.2.40->langchain-openai) (2.33.2)
Requirement already satisfied: charset_normalizer<4,>=2 in c:\users\<USER>\anaconda3\lib\site-packages (from requests>=2.26.0->tiktoken<1,>=0.7->langchain-openai) (2.0.4)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\anaconda3\lib\site-packages (from requests>=2.26.0->tiktoken<1,>=0.7->langchain-openai) (2.5.0)
Requirement already satisfied: colorama in c:\users\<USER>\anaconda3\lib\site-packages (from tqdm>4->openai<2.0.0,>=1.40.0->langchain-openai) (0.4.6)
