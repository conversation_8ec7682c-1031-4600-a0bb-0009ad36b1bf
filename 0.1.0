Requirement already satisfied: langchain-core in c:\users\<USER>\anaconda3\lib\site-packages (0.2.43)
Requirement already satisfied: pydantic<3,>=1 in c:\users\<USER>\anaconda3\lib\site-packages (from langchain-core) (2.11.7)
Requirement already satisfied: jsonpatch<2.0,>=1.33 in c:\users\<USER>\anaconda3\lib\site-packages (from langchain-core) (1.33)
Requirement already satisfied: PyYAML>=5.3 in c:\users\<USER>\anaconda3\lib\site-packages (from langchain-core) (6.0)
Requirement already satisfied: tenacity!=8.4.0,<9.0.0,>=8.1.0 in c:\users\<USER>\anaconda3\lib\site-packages (from langchain-core) (8.5.0)
Requirement already satisfied: typing-extensions>=4.7 in c:\users\<USER>\anaconda3\lib\site-packages (from langchain-core) (4.14.1)
Requirement already satisfied: packaging<25,>=23.2 in c:\users\<USER>\anaconda3\lib\site-packages (from langchain-core) (24.2)
Requirement already satisfied: langsmith<0.2.0,>=0.1.112 in c:\users\<USER>\anaconda3\lib\site-packages (from langchain-core) (0.1.147)
Requirement already satisfied: jsonpointer>=1.9 in c:\users\<USER>\anaconda3\lib\site-packages (from jsonpatch<2.0,>=1.33->langchain-core) (2.1)
Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in c:\users\<USER>\anaconda3\lib\site-packages (from langsmith<0.2.0,>=0.1.112->langchain-core) (1.0.0)
Requirement already satisfied: requests<3,>=2 in c:\users\<USER>\anaconda3\lib\site-packages (from langsmith<0.2.0,>=0.1.112->langchain-core) (2.32.4)
Requirement already satisfied: httpx<1,>=0.23.0 in c:\users\<USER>\anaconda3\lib\site-packages (from langsmith<0.2.0,>=0.1.112->langchain-core) (0.28.1)
Requirement already satisfied: orjson<4.0.0,>=3.9.14 in c:\users\<USER>\anaconda3\lib\site-packages (from langsmith<0.2.0,>=0.1.112->langchain-core) (3.10.18)
Requirement already satisfied: typing-inspection>=0.4.0 in c:\users\<USER>\anaconda3\lib\site-packages (from pydantic<3,>=1->langchain-core) (0.4.1)
Requirement already satisfied: annotated-types>=0.6.0 in c:\users\<USER>\anaconda3\lib\site-packages (from pydantic<3,>=1->langchain-core) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in c:\users\<USER>\anaconda3\lib\site-packages (from pydantic<3,>=1->langchain-core) (2.33.2)
Requirement already satisfied: anyio in c:\users\<USER>\anaconda3\lib\site-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.112->langchain-core) (4.9.0)
Requirement already satisfied: idna in c:\users\<USER>\anaconda3\lib\site-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.112->langchain-core) (3.4)
Requirement already satisfied: certifi in c:\users\<USER>\anaconda3\lib\site-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.112->langchain-core) (2025.1.31)
Requirement already satisfied: httpcore==1.* in c:\users\<USER>\anaconda3\lib\site-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.112->langchain-core) (1.0.9)
Requirement already satisfied: h11>=0.16 in c:\users\<USER>\anaconda3\lib\site-packages (from httpcore==1.*->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.112->langchain-core) (0.16.0)
Requirement already satisfied: charset_normalizer<4,>=2 in c:\users\<USER>\anaconda3\lib\site-packages (from requests<3,>=2->langsmith<0.2.0,>=0.1.112->langchain-core) (2.0.4)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\anaconda3\lib\site-packages (from requests<3,>=2->langsmith<0.2.0,>=0.1.112->langchain-core) (2.5.0)
Requirement already satisfied: sniffio>=1.1 in c:\users\<USER>\anaconda3\lib\site-packages (from anyio->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.112->langchain-core) (1.2.0)
Requirement already satisfied: exceptiongroup>=1.0.2 in c:\users\<USER>\anaconda3\lib\site-packages (from anyio->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.112->langchain-core) (1.1.1)
