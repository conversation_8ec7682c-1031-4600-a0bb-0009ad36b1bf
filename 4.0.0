Collecting flask-cors
  Downloading flask_cors-6.0.1-py3-none-any.whl (13 kB)
Requirement already satisfied: flask>=0.9 in c:\users\<USER>\anaconda3\lib\site-packages (from flask-cors) (3.1.0)
Requirement already satisfied: Werkzeug>=0.7 in c:\users\<USER>\anaconda3\lib\site-packages (from flask-cors) (3.1.3)
Requirement already satisfied: blinker>=1.9 in c:\users\<USER>\anaconda3\lib\site-packages (from flask>=0.9->flask-cors) (1.9.0)
Requirement already satisfied: Jinja2>=3.1.2 in c:\users\<USER>\anaconda3\lib\site-packages (from flask>=0.9->flask-cors) (3.1.2)
Requirement already satisfied: click>=8.1.3 in c:\users\<USER>\anaconda3\lib\site-packages (from flask>=0.9->flask-cors) (8.1.8)
Requirement already satisfied: itsdangerous>=2.2 in c:\users\<USER>\anaconda3\lib\site-packages (from flask>=0.9->flask-cors) (2.2.0)
Requirement already satisfied: importlib-metadata>=3.6 in c:\users\<USER>\anaconda3\lib\site-packages (from flask>=0.9->flask-cors) (4.11.3)
Requirement already satisfied: MarkupSafe>=2.1.1 in c:\users\<USER>\anaconda3\lib\site-packages (from Werkzeug>=0.7->flask-cors) (2.1.1)
Requirement already satisfied: colorama in c:\users\<USER>\anaconda3\lib\site-packages (from click>=8.1.3->flask>=0.9->flask-cors) (0.4.6)
Requirement already satisfied: zipp>=0.5 in c:\users\<USER>\anaconda3\lib\site-packages (from importlib-metadata>=3.6->flask>=0.9->flask-cors) (3.11.0)
Installing collected packages: flask-cors
Successfully installed flask-cors-6.0.1
